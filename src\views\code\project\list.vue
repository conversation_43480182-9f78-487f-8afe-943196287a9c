<template>
  <div class="app-container">
    <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="代码库名称" prop="pName">
        <el-input
          v-model="queryForm.pName"
          placeholder="请输入代码库名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="代码库路径" prop="pNamespace">
        <el-input
          v-model="queryForm.pNamespace"
          placeholder="请输入代码库路径"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="代码库描述" prop="pDesc">
        <el-input
          v-model="queryForm.pDesc"
          placeholder="请输入代码库描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="负责开发组">
        <el-select
          clearable
          placeholder=""
          v-model="queryForm.pDevDept">
          <el-option
            v-for="item in dict.type.code_dev_dept"
            :key="item.value"
            :value="item.value"
            :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="负责测试组">
        <el-select
          clearable
          placeholder=""
          v-model="queryForm.pTestDept">
          <el-option
            v-for="item in dict.type.code_test_dept"
            :key="item.value"
            :value="item.value"
            :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="业务大类">
        <el-select
          clearable
          placeholder=""
          v-model="queryForm.pBroadBusiness">
          <el-option
            v-for="item in broadBusinessList"
            :key="item.id"
            :value="item.id"
            :label="item.name"/>
        </el-select>
      </el-form-item>
      <el-form-item label="业务小类">
        <el-select
          clearable
          placeholder=""
          v-model="queryForm.pNarrowBusiness">
          <el-option
            v-for="item in narrowBusinessList"
            :key="item.id"
            :value="item.id"
            :label="item.name"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="ProjectList" @selection-change="handleSelectionChange" @sort-change="handleSortChange" height="calc(100vh - 290px)" stripe border>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="代码库ID" align="center" prop="pId" v-if="true" sortable="custom"/>
      <el-table-column label="代码库名称" align="center" prop="pName"  />
      <el-table-column label="代码库描述" align="center" prop="pDesc" width="200" />
      <el-table-column label="代码库路径" align="center" prop="pNamespace" width="200" />
      <el-table-column label="分支数" align="center" prop="pBranchCount" sortable="custom" width="120"/>
      <el-table-column label="主干分支Master" align="center" prop="pHasMaster" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.pHasMaster === 1 ? '有' : (scope.row.pHasMaster === 0 ? '无' : '') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="主干(Master)是否受保护" align="center" prop="pMasterProtected" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.pMasterProtected === 1 ? '有' : (scope.row.pMasterProtected === 0 ? '无' : '') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建日期" align="center" prop="pCreatetime" sortable="custom" width="180" >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.pCreatetime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="web访问地址" align="center" prop="pWebUrl" width="200">
        <template slot-scope="scope">
          <a :href="scope.row.pWebUrl" target="_blank">{{ scope.row.pWebUrl }}</a>
        </template>
      </el-table-column>
      <el-table-column label="可视性" align="center" prop="pVisibility" />

      <el-table-column label="负责开发组" width="120" prop="pDevDeptName"></el-table-column>
      <el-table-column label="负责测试组" width="120" prop="pTestDeptName"></el-table-column>
      <el-table-column label="业务大类" width="120" prop="pBroadBusinessName" sortable="custom"></el-table-column>
      <el-table-column label="业务小类" width="120" prop="pNarrowBusinessName" sortable="custom"></el-table-column>

      <el-table-column label="入库时间" align="center" prop="pSyncDatetime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.pSyncDatetime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="openEdit(scope.row)"
          >编辑</el-button>

          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
          @click="handleList(scope.row)"
          >查看分支列表</el-button>

        </template>
      </el-table-column>
    </el-table>

    <div class="container">
    <el-pagination
      class="rear-page"
      :current-page="queryForm.pageNo"
      :page-size="queryForm.pageSize"
      layout="prev, pager, next, slot, jumper, sizes, total"
      :total="queryForm.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <!-- slot部分，跳转末页 -->
      <button
        class="lastPage"
        :disabled="queryForm.lastPageDisabled"
        @click="toLastPage"
      >
        <i class="el-icon-d-arrow-right"></i>
      </button>
    </el-pagination>
    <el-pagination
      class="ahead-page"
      :current-page="queryForm.pageNo"
      :page-size="queryForm.pageSize"
      layout="slot"
      :total="queryForm.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
      <!-- slot部分，跳转首页 -->
      <button
        class="firstPage"
        :disabled="queryForm.firstPageDisabled"
        @click="toFirstPage"
      >
        <i class="el-icon-d-arrow-left"></i>
      </button>
    </el-pagination>
     </div>

    <!-- 添加或修改项目管理对话框 -->
    <el-dialog :title="title" :visible.sync="open"  width="1200px" center append-to-body>
      <el-table v-loading="loadingBranch" :data="branchList" @selection-change="handleSelectionBranchChange"  height="650">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="分支名称" align="center" prop="bname"/>
      <el-table-column label="保护分支" align="center" prop="bisprotected" />
      <el-table-column label="开发可提交" align="center" prop="bdevpush" />
    </el-table>

        <div class="container">
    <el-pagination
      class="rear-page"
      :current-page="queryBranchForm.pageNo"
      :page-size="queryBranchForm.pageSize"
      layout="prev, pager, next, slot, jumper, sizes, total"
      :total="queryBranchForm.total"
      @size-change="handleBranchSizeChange"
      @current-change="handleBranchCurrentChange"
    >
      <!-- slot部分，跳转末页 -->
      <button
        class="lastPage"
        :disabled="queryBranchForm.lastPageDisabled"
        @click="toBranchLastPage"
      >
        <i class="el-icon-d-arrow-right"></i>
      </button>
    </el-pagination>
    <el-pagination
      class="ahead-page"
      :current-page="queryBranchForm.pageNo"
      :page-size="queryBranchForm.pageSize"
      layout="slot"
      :total="queryBranchForm.total"
      @size-change="handleBranchSizeChange"
      @current-change="handleBranchCurrentChange"
    >
      <!-- slot部分，跳转首页 -->
      <button
        class="firstPage"
        :disabled="queryBranchForm.firstPageDisabled"
        @click="toBranchFirstPage"
      >
        <i class="el-icon-d-arrow-left"></i>
      </button>
    </el-pagination>
     </div>
      <div slot="footer" class="dialog-footer">
      </div>
    </el-dialog>


    <el-dialog title="代码库详情" :visible.sync="dialogFormVisible" width="40%">
      <el-form :model="project">
        <el-form-item label="代码库名称" :label-width="formLabelWidth">
          <el-input v-model="project.pName" readonly></el-input>
        </el-form-item>
        <el-form-item label="代码库描述" :label-width="formLabelWidth">
          <el-input v-model="project.pDesc"  readonly></el-input>
        </el-form-item>
        <el-form-item label="负责开发组" :label-width="formLabelWidth">
          <el-select
            clearable
            placeholder="请选择负责开发组"
            v-model="project.pDevDept" >
            <el-option
              v-for="item in dict.type.code_dev_dept.filter(item => item.value !== '0')"
              :key="item.value"
              :value="item.value"
              :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="负责测试组" :label-width="formLabelWidth">
          <el-select
            clearable
            placeholder="请选择负责测试组"
            v-model="project.pTestDept">
            <el-option
              v-for="item in dict.type.code_test_dept.filter(item => item.value !== '0')"
              :key="item.value"
              :value="item.value"
              :label="item.label"/>
          </el-select>
        </el-form-item>
        <el-form-item label="业务大类" :label-width="formLabelWidth">
          <el-select
            clearable
            placeholder="请选择业务大类"
            v-model="project.pBroadBusiness">
            <el-option
              v-for="item in broadBusinessList.filter(item => item.id !== 0)"
              :key="item.id"
              :value="item.id"
              :label="item.name"/>
          </el-select>
        </el-form-item>
        <el-form-item label="业务小类" :label-width="formLabelWidth">
          <el-select
            clearable
            placeholder="请选择业务小类"
            v-model="project.pNarrowBusiness">
            <el-option
              v-for="item in narrowBusinessList.filter(item => item.id !== 0)"
              :key="item.id"
              :value="item.id"
              :label="item.name"/>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="updateProject">确 定</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { listProject, listBranch, editProject } from "@/api/Project";
import { businessConfigByType } from '@/api/system/businessConfig'
import { getListByType } from '@/api/system/dept'

export default {
  name: "Project",
  dicts: ['code_dev_dept', 'code_test_dept'],
  data() {
    return {
      formLabelWidth:'120px',
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: false,
      loadingBranch: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 项目管理表格数据
      ProjectList: [],
      // 项目分支列表
      branchList: [] ,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //项目详情
      project: {},
      dialogFormVisible: false,
      // 查询参数
      queryForm: {
        pageNum: 1,
        pageSize: 50,
        pName: '',
        pNamespace: '',
        pDesc: '',
        total: 0,
        firstPageDisabled: false, //  首页
        lastPageDisabled: true, //  末页
        pDevDept: '0',//负责开发组
        pTestDept: '0',//负责测试组
        pBroadBusiness: 0,//业务大类
        pNarrowBusiness: 0,//业务小类
        orderByField: '', // 排序字段
        orderRule: '', // 排序规则
      },
      broadBusinessList: [],//业务大类
      narrowBusinessList: [],//业务小类
      busiconfigParam: {
        configType: null
      },
      queryBranchForm: {
        pageNum: 1,
        pageSize: 50,
        total: 0,
        firstPageDisabled: false, //  首页
        lastPageDisabled: true, //  末页
      },
    };
  },
  watch: {
        // 分页 计算首页和末页
        queryForm: {
          handler(newVal) {
            let pages = Math.ceil(newVal.total / newVal.pageSize);
        if (pages === 0) {
          // 数据(totalResult)为0
          this.queryForm.firstPageDisabled = true; // 首页按钮是否禁用
          this.queryForm.lastPageDisabled = true; // 末页按钮是否禁用
        } else {
          this.queryForm.firstPageDisabled = newVal.pageNo === 1;
          this.queryForm.lastPageDisabled = newVal.pageNo === pages;
        }
          },
          // 一进页面就执行
          immediate: false,
          deep: true,
        },
         // 分页 计算首页和末页
         queryBranchForm: {
          handler(newVal) {
            let pages = Math.ceil(newVal.total / newVal.pageSize);
        if (pages === 0) {
          // 数据(totalResult)为0
          this.queryBranchForm.firstPageDisabled = true; // 首页按钮是否禁用
          this.queryBranchForm.lastPageDisabled = true; // 末页按钮是否禁用
        } else {
          this.queryBranchForm.firstPageDisabled = newVal.pageNo === 1;
          this.queryBranchForm.lastPageDisabled = newVal.pageNo === pages;
        }
          },
          // 一进页面就执行
          immediate: false,
          deep: true,
        },
      },
  created() {
    this.fetchData();
  },
  methods: {
    // 获取业务配置
    async geBusinessConfigList(configType) {
      const res = await businessConfigByType(configType);
      if (res.code === 200) {
        return res.data;
      }
    },
    async fetchData() {
      //业务大类
      this.busiconfigParam.configType = 1;
      this.broadBusinessList = await this.geBusinessConfigList(this.busiconfigParam);
      //业务小类
      this.busiconfigParam.configType = 2;
      this.narrowBusinessList = await this.geBusinessConfigList(this.busiconfigParam);
    },
    //打开编辑框
    openEdit(project){
      console.log(project)
      this.project = project;
      this.dialogFormVisible = true;
    },
    //编辑代码库
    updateProject(){
      editProject(this.project).then(res => {
        if (res.code === 200){
          this.$message.success("修改成功");
          this.dialogFormVisible = false;
          this.getList();
        }else{
          this.$message.error("修改失败");
        }
      })
    },
    //排序
    handleSortChange({ prop, order }) {
        if (order === 'descending') {
          this.queryForm.orderByField = prop;
          this.queryForm.orderRule = 'desc';
        } else if (order === 'ascending') {
          this.queryForm.orderByField = prop;
          this.queryForm.orderRule = 'asc';
        } else {
          // 如果没有指定排序方向，则清空排序条件
          this.queryForm.orderByField = '';
          this.queryForm.orderRule = '';
        }
        // 更新查询条件后重新获取列表
        this.getList();
    },
    //  改变页码
    handlePageChange({ pageNo, pageSize }) {
      this.queryForm.pageNo = pageNo;
      this.queryForm.pageSize = pageSize;
    },

    //  改变每页显示数量
    handleSizeChange(pageSize) {
      this.queryForm.pageSize = pageSize;
    },

    //  改变当前页码
    handleCurrentChange(pageNo) {
      this.queryForm.pageNo = pageNo;
    },

    //  前往首页
    toFirstPage() {
      this.handleCurrentChange(1);
    },

    //  前往末页
    toLastPage() {
      let max = Math.ceil(this.queryForm.total / this.queryForm.pageSize);
      this.handleCurrentChange(max);
    },
    // 当前页面
    handleCurrentChange(val) {
        this.queryForm.pageNo = val
        console.log('handleCurrentChange',val)
        this.getList()
      },

    // 页数
    handleSizeChange(val) {
        this.queryForm.pageSize = val
        console.log('handleSizeChange',val)
        this.getList()
      },
    /** 查询项目管理列表 */
    getList() {
      this.loading = true;
      listProject(this.queryForm).then(response => {
        console.log('response = ',response)
        this.ProjectList = response.data.records;
        this.queryForm.total = response.data.total;
        this.loading = false;
      });
    },
    /** 查询项目分支列表 */
    getBranchList(data) {
      this.loading = true;
      listBranch(data).then(response => {
        console.log('response = ',response)
        this.branchList = response.data.records;
        this.queryBranchForm.total = response.data.total;
        this.loadingBranch = false;
        this.loading =false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        pName: '',
        pNamespace: '',
        pDesc: '',
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryForm.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryForm.pDevDept = '0';
      this.queryForm.pTestDept = '0';
      this.queryForm.pBroadBusiness = 0;
      this.queryForm.pNarrowBusiness = 0;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.pId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    handleSelectionBranchChange(val){

    },
    /** 新增按钮操作 */
    handleList(data) {
      console.log('data = ' , data.pId)
      this.open = true;
      if(data.pName === null){
        this.title = "项目分支列表";
      }else{
        this.title = '项目 '+data.pName+' 列表';
      }
      data.pid = data.pId || null;
      this.getBranchList(data)
    },

    //  改变页码
    handleBranchPageChange({ pageNo, pageSize }) {
      this.queryBranchForm.pageNo = pageNo;
      this.queryBranchForm.pageSize = pageSize;
    },

    //  改变每页显示数量
    handleBranchSizeChange(pageSize) {
      this.queryBranchForm.pageSize = pageSize;
    },

    //  改变当前页码
    handleBranchCurrentChange(pageNo) {
      this.queryBranchForm.pageNo = pageNo;
    },

    //  前往首页
    toBranchFirstPage() {
      this.handleBranchCurrentChange(1);
    },

    //  前往末页
    toBranchLastPage() {
      let max = Math.ceil(this.queryForm.total / this.queryForm.pageSize);
      this.handleBranchCurrentChange(max);
    },
    // 当前页面
    handleBranchCurrentChange(val) {
        this.queryBranchForm.pageNo = val
        this.getBranchList()
      },

    // 页数
    handleBranchSizeChange(val) {
        this.queryBranchForm.pageSize = val
        this.getBranchList()
      },
  }
};
</script>

<style lang="scss" scoped>
    .container{
        float: left;
     }
      .el-pagination {
      float: right;
      margin-top: 10px;
    }
    .el-pagination.ahead-page {
      padding-right: 0px;
    }
    .el-pagination.rear-page {
      padding-left: 0px;
    }
    .firstPage,
    .lastPage {
      background-color: white;
      cursor: pointer;
    }
    .el-dialog .el-dialog__body{
      display: flex;
      justify-content: center;
      align-items: center;
}
</style>
