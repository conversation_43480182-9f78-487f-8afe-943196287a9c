<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="查询年份">
          <el-select
                  v-model="queryParams.evalYear"
                  maxlength="4"
                  placeholder="选择查询年份"
                  clearable
                  size="small"
                  style="width: 160px"
          >
          <el-option
                  v-for="year in years"
                  :key="year"
                  :label="year"
                  :value="year"
          />
          </el-select>
      </el-form-item>
      <el-form-item label="查询月份">
          <el-select
                  v-model="queryParams.evalMonth"
                  placeholder="选择查询月份"
                  clearable
                  size="small"
                  maxlength="2"
                  style="width: 160px"
          >
          <el-option
                  v-for="dict in monthDict"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
          />
          </el-select>
      </el-form-item>
      <el-form-item label="岗位类别:">
        <el-select
          v-model="queryParams.postType"
          placeholder="选择岗位类别"
          clearable
          size="small"
          style="width: 160px"
        >
          <el-option
            v-for="dict in postTypeDict"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属组:">
          <el-select
                  v-model="queryParams.groupId"
                  placeholder="选择组别"
                  clearable
                  size="small"
                  style="width: 160px"
          >
          <el-option
                  v-for="dict in groupDict"
                  :key="dict.deptId"
                  :label="dict.deptName"
                  :value="dict.deptId"
          />
          </el-select>
      </el-form-item>
      <el-form-item label="姓名" prop="userName">
          <el-input
                  v-model="queryParams.userName"
                  placeholder="请输入姓名"
                  clearable
          />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getList">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:trackWorkResult:export']"
        >导出</el-button>
      </el-form-item>
    </el-form>

    <el-table ref="trackWorkResultTable" v-loading="loading" :data="trackWorkResultList" height="calc(100vh - 180px)" stripe border>
      <el-table-column label="序号" align="center" width="50" fixed>
        <template slot-scope="scop">
          {{scop.$index+1}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="姓名" prop="workUsername" :show-overflow-tooltip="true" width="80" fixed>
        <template slot-scope="scope">
          <div class="link-type" @click="toWorkDetail(scope.row)">{{ scope.row.workUsername }}</div>
        </template>
      </el-table-column>
      <el-table-column v-if="showYearMonth" align="center" label="年" prop="workYear" width="60" fixed/>
      <el-table-column v-if="showYearMonth" align="center" label="月" prop="workMonth" :show-overflow-tooltip="true" width="50" fixed/>
      <el-table-column align="center" label="所属组名" prop="workGroup" :show-overflow-tooltip="true" width="120" fixed/>
      <el-table-column align="center" label="岗位" prop="roleName" :show-overflow-tooltip="true" width="100" fixed/>
      <el-table-column align="center" label="工作时长(小时)" prop="kqAttendanceWorkTime" :show-overflow-tooltip="true" width="150" :sortable="true"
        :sort-method="(a, b) => parseFloat(a.kqAttendanceWorkTime) - parseFloat(b.kqAttendanceWorkTime)"
      >
        <template slot-scope="scope">
          {{ parseFloat(scope.row.kqAttendanceWorkTime).toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="请假时长(小时)" prop="kqLeaveTime" :show-overflow-tooltip="true" width="150" :sortable="true"
                       :sort-method="(a, b) => parseFloat(a.kqLeaveTime) - parseFloat(b.kqLeaveTime)"
      >
        <template slot-scope="scope">
          {{ parseFloat(scope.row.kqLeaveTime).toFixed(2) }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="耗时超过10天的任务" prop="workOverTenDaysTaskCount" :show-overflow-tooltip="true" width="180"  sortable/>
      <el-table-column align="center" label="禅道填写耗时(小时)" prop="workConsumedTime" :show-overflow-tooltip="true" width="150"  sortable/>
      <el-table-column align="center" label="完成禅道任务" prop="allWorkTaskCount" :show-overflow-tooltip="true" width="130"   sortable/>
      <el-table-column align="center" label="开发解决bug数" prop="workResolveBugCount" :show-overflow-tooltip="true" width="140"   sortable/>
      <el-table-column align="center" label="调整代码(小于1000行的新增)" prop="pushCodeLines" :show-overflow-tooltip="true" width="140"   sortable/>
      <el-table-column align="center" label="创建BUG" prop="createBugCount" :show-overflow-tooltip="true" width="130"  sortable/>
      <el-table-column align="center" label="测试关闭BUG" prop="workCloseBugCount" :show-overflow-tooltip="true" width="130"  sortable/>
      <el-table-column align="center" label="完成测试用例" prop="workCaseCount" :show-overflow-tooltip="true" width="140"  sortable/>
      <el-table-column align="center" label="文档" prop="docCount" :show-overflow-tooltip="true"  sortable/>
      <el-table-column align="center" label="预警" prop="warnCount" :show-overflow-tooltip="true"  sortable/>
    </el-table>

  </div>
</template>

<script>
import { trackWorkResultList } from "@/api/business/trackWorkResult";
import {getDicts} from "../../../api/system/dict/data";
import {deptSelect} from "../../../api/commonBiz";

export default {
  name: "TrackWorkResult",
  data() {
    return {
      // 按钮loading
      buttonLoading: true,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      taskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      monthDict:[],
      groupDict:[],
      postTypeDict:[],
      years: [],
      trackWorkResultList:[],
      showYearMonth: true,  // 添加显示年月列控制变量
      queryParams: {
        pageNum: 1,
        pageSize: 5000,
        evalYear:undefined,
        evalMonth:undefined,
        groupId:null,
        postType:"1",
        userName:undefined,
      },
    };
  },
  created() {
    this.createYear();
    this.init();
    this.handleSearch();
  },
  methods: {
    createYear(){
        // 获取当前年份和前两年的年份
        const currentYear = new Date().getFullYear();
        const startYear = 2021;

        // 构造年份选择器数据源
        for (let i = currentYear; i >= startYear; i--) {
            this.years.push(i.toString());
        }
        this.evalYear = currentYear;
    },
    handleSearch() {

        if(this.queryParams.evalYear === undefined || this.queryParams.evalYear === '') {
            alert("请选择查询年份!")
            return
        }
      this.getList();
    },
    async init() {
      const now = new Date()
      this.queryParams.evalYear = now.getFullYear()
      this.queryParams.evalMonth = now.getMonth()+1
      await getDicts("month").then(response => {
        this.monthDict = response.data
      })
      this.getDeptList()
      await getDicts("post_type").then(response => {
        this.postTypeDict = response.data
      })
    },

    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      // 设置是否显示年月列
      this.showYearMonth = this.queryParams.evalMonth !== '-1';
      trackWorkResultList(this.queryParams).then(response => {
        this.trackWorkResultList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.$nextTick(() => {
          this.$refs.trackWorkResultTable.doLayout() // 重新布局页面元素，解决数据变化导致滚动条显示空白格问题
        })
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('business/trackWorkResult/export', {
        ...this.queryParams
      }, `${this.queryParams.evalYear}年${this.queryParams.evalMonth}月工作成果跟踪_${new Date().getTime()}.csv`)
    },
    /** 查询部门列表 */
    getDeptList() {
      var deptList = [];
      deptSelect().then(response => {
        deptList = response.data;
        let tecCenter = {deptName: "技术中心", deptId: 101}
        deptList.unshift(tecCenter)
        this.groupDict = deptList;
        this.queryParams.groupId = deptList[0].deptId;
      });
    },
    // 跳转工作明细
    toWorkDetail (row) {
      let params = {
        evalYear: this.queryParams.evalYear,
        evalMonth: this.queryParams.evalMonth,
        workUsername: row.workUsername,
      }
      this.$router.push({ name: 'WorkDetail', query: { params: JSON.stringify(params) }, params: { fromList: true}})
    }
  }
};
</script>
